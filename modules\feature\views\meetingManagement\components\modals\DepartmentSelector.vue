<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="600px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm"> 确 定 </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'DepartmentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {};
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    methods: {
        handleClose() {
            this.resetForm();
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped></style>
